[package]
name = "mclayout"
version = "0.1.0"
edition = "2021"
description = "A parser for circuit description files in JSON format"
license = "MIT"
repository = "https://github.com/example/mclayout"
keywords = ["circuit", "parser", "electronics", "json"]
categories = ["parsing", "science"]

[dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
thiserror = "1.0"

[[example]]
name = "basic_usage"
path = "examples/basic_usage.rs"

[[example]]
name = "simple_test"
path = "examples/simple_test.rs"
