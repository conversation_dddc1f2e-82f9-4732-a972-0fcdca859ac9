//! # Circuit Description Parser
//!
//! This crate provides a parser for circuit description files in JSON format.
//! It can parse hierarchical circuit designs with modules, components, connections, and parameters.
//!
//! ## Features
//!
//! - Parse JSON circuit description files
//! - Hierarchical module support
//! - Component parameter handling
//! - Connection validation
//! - Comprehensive error reporting
//! - Convenient API for accessing circuit data
//!
//! ## Quick Start
//!
//! ```rust
//! use mclayout::{CircuitParser, Result};
//!
//! fn main() -> Result<()> {
//!     // Parse from file
//!     let circuit = CircuitParser::parse_from_file("circuit.json")?;
//!
//!     // Access the main module
//!     if let Some(main_module) = circuit.main_module() {
//!         println!("Main module: {}", main_module.instance);
//!
//!         // Find components
//!         for component in main_module.components() {
//!             println!("Component: {} ({})", component.instance, component.class);
//!         }
//!     }
//!
//!     // Find specific modules
//!     if let Some(power_module) = circuit.find_module("pwusb") {
//!         println!("Found power module: {}", power_module.class);
//!     }
//!
//!     Ok(())
//! }
//! ```
//!
//! ## Data Structure Overview
//!
//! The parser represents circuits using the following main structures:
//!
//! - [`CircuitDescription`]: Root structure containing modules and component definitions
//! - [`Module`]: Hierarchical circuit blocks that can contain other modules and components
//! - [`Component`]: Individual circuit elements with parameters
//! - [`Net`]: Network connections between components
//! - [`Bus`]: Grouped signals for multi-wire connections
//!
//! ## Validation
//!
//! The parser includes comprehensive validation:
//!
//! ```rust
//! use mclayout::CircuitParser;
//!
//! let circuit = CircuitParser::parse_from_file("circuit.json")?;
//!
//! // Perform additional validation
//! CircuitParser::validate_comprehensive(&circuit)?;
//! ```

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use thiserror::Error;

/// Errors that can occur during circuit parsing
#[derive(Error, Debug)]
pub enum CircuitParseError {
    #[error("JSON parsing error: {0}")]
    JsonError(#[from] serde_json::Error),
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    #[error("Validation error: {0}")]
    ValidationError(String),
    #[error("Missing required field: {0}")]
    MissingField(String),
}

/// Type alias for Result with CircuitParseError
pub type Result<T> = std::result::Result<T, CircuitParseError>;

/// Root structure representing the entire circuit description
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitDescription {
    pub modules: Vec<Module>,
    #[serde(default)]
    pub components: Vec<ComponentDefinition>,
}

/// Represents a circuit module (hierarchical block)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Module {
    #[serde(rename = "type")]
    pub module_type: String,
    pub class: String,
    pub instance: String,
    #[serde(default)]
    pub objects: Vec<CircuitObject>,
    #[serde(default)]
    pub pins: ModulePins,
    #[serde(default)]
    pub nets: Vec<Net>,
    #[serde(default)]
    pub buses: Vec<Bus>,
}

/// Circuit objects that can be contained within modules
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum CircuitObject {
    #[serde(rename = "module")]
    Module(Module),
    #[serde(rename = "component")]
    Component(Component),
    #[serde(rename = "series")]
    Series(SeriesGroup),
}

/// Individual component instance
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Component {
    pub class: String,
    pub instance: String,
    #[serde(default)]
    pub params: HashMap<String, serde_json::Value>,
}

/// Series group of components
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SeriesGroup {
    pub instance: String,
    pub member: Vec<Component>,
}

/// Module pin definition
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ModulePin {
    Simple(String),
    Detailed {
        name: String,
        #[serde(rename = "type")]
        pin_type: PinType,
    },
}

/// Module pins can be either a simple string array or detailed pin objects
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ModulePins {
    Simple(Vec<String>),
    Detailed(Vec<ModulePin>),
}

impl Default for ModulePins {
    fn default() -> Self {
        ModulePins::Simple(Vec::new())
    }
}

impl ModulePins {
    /// Get the number of pins
    pub fn len(&self) -> usize {
        match self {
            ModulePins::Simple(pins) => pins.len(),
            ModulePins::Detailed(pins) => pins.len(),
        }
    }

    /// Check if pins collection is empty
    pub fn is_empty(&self) -> bool {
        self.len() == 0
    }
}

/// Pin direction/type
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum PinType {
    Input,
    Output,
    Inout,
}

/// Network connection definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Net {
    #[serde(default)]
    pub name: String,
    #[serde(rename = "type", default)]
    pub net_type: Option<String>,
    #[serde(default)]
    pub connection: Vec<String>,
    #[serde(default)]
    pub signals: Vec<BusSignal>,
}

/// Bus definition for grouped signals
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Bus {
    pub name: String,
    #[serde(default)]
    pub signals: Vec<String>,
    #[serde(default)]
    pub labels: Vec<String>,
}

/// Individual signal within a bus
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BusSignal {
    pub name: String,
    pub connection: Vec<String>,
}

/// Component definition/library entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentDefinition {
    #[serde(default)]
    pub name: String,
    #[serde(default)]
    pub class: String,
    #[serde(default)]
    pub partno: String,
    #[serde(default)]
    pub package: String,
    #[serde(default)]
    pub attr: HashMap<String, serde_json::Value>,
    #[serde(default)]
    pub pins: Vec<ComponentPin>,
    #[serde(default)]
    pub nets: Vec<Net>,
    #[serde(default)]
    pub buses: Vec<Bus>,
    #[serde(default)]
    pub objects: Vec<CircuitObject>,
}

/// Component pin definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentPin {
    pub id: serde_json::Value, // Can be number or string
    #[serde(default)]
    pub name: Vec<String>,
    #[serde(default)]
    pub attr: HashMap<String, serde_json::Value>,
}

/// Circuit parser for loading and parsing circuit description files
pub struct CircuitParser;

impl CircuitParser {
    /// Create a new circuit parser
    pub fn new() -> Self {
        Self
    }

    /// Parse a circuit description from a JSON string
    pub fn parse_from_str(json_str: &str) -> Result<CircuitDescription> {
        let circuit: CircuitDescription = serde_json::from_str(json_str)?;
        Self::validate_circuit(&circuit)?;
        Ok(circuit)
    }

    /// Parse a circuit description from a file
    pub fn parse_from_file<P: AsRef<std::path::Path>>(path: P) -> Result<CircuitDescription> {
        let content = std::fs::read_to_string(path)?;
        Self::parse_from_str(&content)
    }

    /// Parse a circuit description from a reader
    pub fn parse_from_reader<R: std::io::Read>(reader: R) -> Result<CircuitDescription> {
        let circuit: CircuitDescription = serde_json::from_reader(reader)?;
        Self::validate_circuit(&circuit)?;
        Ok(circuit)
    }

    /// Validate the parsed circuit description
    fn validate_circuit(circuit: &CircuitDescription) -> Result<()> {
        // Validate that we have at least one module
        if circuit.modules.is_empty() {
            return Err(CircuitParseError::ValidationError(
                "Circuit must contain at least one module".to_string(),
            ));
        }

        // Validate each module
        for module in &circuit.modules {
            Self::validate_module(module)?;
        }

        // Validate component definitions
        for component in &circuit.components {
            Self::validate_component_definition(component)?;
        }

        Ok(())
    }

    /// Validate a module
    fn validate_module(module: &Module) -> Result<()> {
        if module.class.is_empty() {
            return Err(CircuitParseError::MissingField("module.class".to_string()));
        }
        if module.instance.is_empty() {
            return Err(CircuitParseError::MissingField("module.instance".to_string()));
        }

        // Validate nested objects
        for object in &module.objects {
            match object {
                CircuitObject::Module(nested_module) => {
                    Self::validate_module(nested_module)?;
                }
                CircuitObject::Component(component) => {
                    Self::validate_component(component)?;
                }
                CircuitObject::Series(series) => {
                    Self::validate_series_group(series)?;
                }
            }
        }

        Ok(())
    }

    /// Validate a component instance
    fn validate_component(component: &Component) -> Result<()> {
        if component.class.is_empty() {
            return Err(CircuitParseError::MissingField("component.class".to_string()));
        }
        if component.instance.is_empty() {
            return Err(CircuitParseError::MissingField("component.instance".to_string()));
        }
        Ok(())
    }

    /// Validate a series group
    fn validate_series_group(series: &SeriesGroup) -> Result<()> {
        if series.instance.is_empty() {
            return Err(CircuitParseError::MissingField("series.instance".to_string()));
        }
        if series.member.is_empty() {
            return Err(CircuitParseError::ValidationError(
                "Series group must contain at least one member".to_string(),
            ));
        }
        for member in &series.member {
            Self::validate_component(member)?;
        }
        Ok(())
    }

    /// Validate a component definition
    fn validate_component_definition(component: &ComponentDefinition) -> Result<()> {
        // Component definitions can have either name or class, but should have some identifier
        if component.name.is_empty() && component.class.is_empty() {
            return Err(CircuitParseError::ValidationError(
                "Component definition must have either name or class".to_string(),
            ));
        }
        Ok(())
    }
}

impl Default for CircuitParser {
    fn default() -> Self {
        Self::new()
    }
}

/// Additional validation and utility methods
impl CircuitParser {
    /// Validate net connections and references
    pub fn validate_connections(circuit: &CircuitDescription) -> Result<()> {
        for module in &circuit.modules {
            Self::validate_module_connections(module, &circuit.components)?;
        }
        Ok(())
    }

    /// Validate connections within a module
    fn validate_module_connections(module: &Module, component_defs: &[ComponentDefinition]) -> Result<()> {
        // Collect all available connection points
        let mut available_pins = std::collections::HashSet::new();

        // Add module pins
        match &module.pins {
            ModulePins::Simple(pin_names) => {
                for name in pin_names {
                    available_pins.insert(name.clone());
                }
            }
            ModulePins::Detailed(pins) => {
                for pin in pins {
                    match pin {
                        ModulePin::Simple(name) => {
                            available_pins.insert(name.clone());
                        }
                        ModulePin::Detailed { name, .. } => {
                            available_pins.insert(name.clone());
                        }
                    }
                }
            }
        }

        // Add component pins from objects
        for object in &module.objects {
            match object {
                CircuitObject::Component(component) => {
                    // Find component definition to get pin information
                    if let Some(comp_def) = component_defs.iter().find(|def|
                        def.name == component.class || def.class == component.class) {
                        for pin in &comp_def.pins {
                            let pin_ref = format!("{}.{}", component.instance, pin.id);
                            available_pins.insert(pin_ref);
                        }
                    }
                }
                CircuitObject::Series(series) => {
                    // Add series connection points
                    available_pins.insert(format!("{}.1", series.instance));
                    available_pins.insert(format!("{}.2", series.instance));
                }
                CircuitObject::Module(nested_module) => {
                    // Recursively validate nested modules
                    Self::validate_module_connections(nested_module, component_defs)?;
                }
            }
        }

        // Validate net connections
        for net in &module.nets {
            for connection in &net.connection {
                if !connection.is_empty() && !available_pins.contains(connection) {
                    // Check if it's a bus signal reference
                    if !connection.contains('.') && !Self::is_valid_bus_reference(connection, module) {
                        return Err(CircuitParseError::ValidationError(
                            format!("Invalid connection reference: {} in module {}", connection, module.instance)
                        ));
                    }
                }
            }

            // Validate bus signals
            for signal in &net.signals {
                for connection in &signal.connection {
                    if !connection.is_empty() && !available_pins.contains(connection) {
                        return Err(CircuitParseError::ValidationError(
                            format!("Invalid bus signal connection: {} in net {}", connection, net.name)
                        ));
                    }
                }
            }
        }

        Ok(())
    }

    /// Check if a connection reference is a valid bus reference
    fn is_valid_bus_reference(connection: &str, module: &Module) -> bool {
        // Check if it's a module pin
        match &module.pins {
            ModulePins::Simple(pin_names) => {
                if pin_names.contains(&connection.to_string()) {
                    return true;
                }
            }
            ModulePins::Detailed(pins) => {
                for pin in pins {
                    match pin {
                        ModulePin::Simple(name) => {
                            if name == connection {
                                return true;
                            }
                        }
                        ModulePin::Detailed { name, .. } => {
                            if name == connection {
                                return true;
                            }
                        }
                    }
                }
            }
        }

        // Check if it's a bus name
        module.buses.iter().any(|bus| bus.name == connection)
    }

    /// Validate parameter values
    pub fn validate_parameters(circuit: &CircuitDescription) -> Result<()> {
        for module in &circuit.modules {
            Self::validate_module_parameters(module)?;
        }
        Ok(())
    }

    /// Validate parameters within a module
    fn validate_module_parameters(module: &Module) -> Result<()> {
        for object in &module.objects {
            match object {
                CircuitObject::Component(component) => {
                    Self::validate_component_parameters(component)?;
                }
                CircuitObject::Series(series) => {
                    for member in &series.member {
                        Self::validate_component_parameters(member)?;
                    }
                }
                CircuitObject::Module(nested_module) => {
                    Self::validate_module_parameters(nested_module)?;
                }
            }
        }
        Ok(())
    }

    /// Validate component parameters
    fn validate_component_parameters(component: &Component) -> Result<()> {
        // Check for common parameter issues
        for (key, value) in &component.params {
            // Check for malformed parameter values
            if let Some(str_val) = value.as_str() {
                if str_val.trim().is_empty() {
                    return Err(CircuitParseError::ValidationError(
                        format!("Empty parameter value for {} in component {}", key, component.instance)
                    ));
                }

                // Check for common typos in parameter names
                if key.contains(' ') && !key.starts_with("unknown-") {
                    return Err(CircuitParseError::ValidationError(
                        format!("Parameter name contains spaces: {} in component {}", key, component.instance)
                    ));
                }
            }
        }
        Ok(())
    }

    /// Perform comprehensive validation
    pub fn validate_comprehensive(circuit: &CircuitDescription) -> Result<()> {
        Self::validate_circuit(circuit)?;
        Self::validate_connections(circuit)?;
        Self::validate_parameters(circuit)?;
        Ok(())
    }
}

/// Convenience methods for accessing circuit data
impl CircuitDescription {
    /// Get the main/root module
    pub fn main_module(&self) -> Option<&Module> {
        self.modules.iter().find(|m| m.class == "main" || m.instance == "main")
    }

    /// Find a module by instance name
    pub fn find_module(&self, instance: &str) -> Option<&Module> {
        for module in &self.modules {
            if let Some(found) = self.find_module_in_tree(module, instance) {
                return Some(found);
            }
        }
        None
    }

    /// Search for a module within a module tree
    fn find_module_in_tree<'a>(&self, module: &'a Module, instance: &str) -> Option<&'a Module> {
        if module.instance == instance {
            return Some(module);
        }

        // Search in nested modules
        for object in &module.objects {
            if let CircuitObject::Module(nested_module) = object {
                if let Some(found) = self.find_module_in_tree(nested_module, instance) {
                    return Some(found);
                }
            }
        }
        None
    }

    /// Find a component definition by name or class
    pub fn find_component_definition(&self, name_or_class: &str) -> Option<&ComponentDefinition> {
        self.components.iter().find(|comp|
            comp.name == name_or_class || comp.class == name_or_class
        )
    }

    /// Get all modules flattened into a list
    pub fn all_modules(&self) -> Vec<&Module> {
        let mut modules = Vec::new();
        for module in &self.modules {
            self.collect_modules_from_tree(module, &mut modules);
        }
        modules
    }

    /// Recursively collect all modules from a module tree
    fn collect_modules_from_tree<'a>(&self, module: &'a Module, collector: &mut Vec<&'a Module>) {
        collector.push(module);
        for object in &module.objects {
            if let CircuitObject::Module(nested_module) = object {
                self.collect_modules_from_tree(nested_module, collector);
            }
        }
    }

    /// Get all component instances across all modules
    pub fn all_components(&self) -> Vec<(&Module, &Component)> {
        let mut components = Vec::new();
        for module in &self.modules {
            self.collect_components_from_tree(module, &mut components);
        }
        components
    }

    /// Recursively collect all component instances from a module tree
    fn collect_components_from_tree<'a>(&self, module: &'a Module, collector: &mut Vec<(&'a Module, &'a Component)>) {
        for object in &module.objects {
            match object {
                CircuitObject::Component(component) => {
                    collector.push((module, component));
                }
                CircuitObject::Series(series) => {
                    for member in &series.member {
                        collector.push((module, member));
                    }
                }
                CircuitObject::Module(nested_module) => {
                    self.collect_components_from_tree(nested_module, collector);
                }
            }
        }
    }
}

/// Convenience methods for Module
impl Module {
    /// Find a component by instance name within this module
    pub fn find_component(&self, instance: &str) -> Option<&Component> {
        for object in &self.objects {
            match object {
                CircuitObject::Component(component) => {
                    if component.instance == instance {
                        return Some(component);
                    }
                }
                CircuitObject::Series(series) => {
                    for member in &series.member {
                        if member.instance == instance {
                            return Some(member);
                        }
                    }
                }
                _ => {}
            }
        }
        None
    }

    /// Find a net by name within this module
    pub fn find_net(&self, name: &str) -> Option<&Net> {
        self.nets.iter().find(|net| net.name == name)
    }

    /// Find a bus by name within this module
    pub fn find_bus(&self, name: &str) -> Option<&Bus> {
        self.buses.iter().find(|bus| bus.name == name)
    }

    /// Get all component instances in this module (non-recursive)
    pub fn components(&self) -> Vec<&Component> {
        let mut components = Vec::new();
        for object in &self.objects {
            match object {
                CircuitObject::Component(component) => {
                    components.push(component);
                }
                CircuitObject::Series(series) => {
                    for member in &series.member {
                        components.push(member);
                    }
                }
                _ => {}
            }
        }
        components
    }

    /// Get all nested modules in this module (non-recursive)
    pub fn nested_modules(&self) -> Vec<&Module> {
        self.objects.iter()
            .filter_map(|obj| match obj {
                CircuitObject::Module(module) => Some(module),
                _ => None,
            })
            .collect()
    }
}

/// Convenience methods for Component
impl Component {
    /// Get a parameter value as a string
    pub fn get_param_str(&self, key: &str) -> Option<&str> {
        self.params.get(key)?.as_str()
    }

    /// Get a parameter value as a number
    pub fn get_param_number(&self, key: &str) -> Option<f64> {
        self.params.get(key)?.as_f64()
    }

    /// Check if component has a specific parameter
    pub fn has_param(&self, key: &str) -> bool {
        self.params.contains_key(key)
    }

    /// Check if component is marked as "NC" (not connected)
    pub fn is_not_connected(&self) -> bool {
        self.get_param_str("connection").map_or(false, |v| v == "NC")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    /// Create a simple test circuit for testing
    fn create_test_circuit() -> CircuitDescription {
        let test_json = r#"
        {
            "modules": [
                {
                    "type": "module",
                    "class": "main",
                    "instance": "main",
                    "objects": [
                        {
                            "type": "component",
                            "class": "RES",
                            "instance": "R1",
                            "params": {
                                "rs": "10kΩ",
                                "pkg": "R0603"
                            }
                        },
                        {
                            "type": "module",
                            "class": "POWER",
                            "instance": "power",
                            "objects": [
                                {
                                    "type": "component",
                                    "class": "CAP",
                                    "instance": "C1",
                                    "params": {
                                        "cap": "100nF",
                                        "volt": "25V"
                                    }
                                }
                            ],
                            "pins": [
                                {"name": "VIN", "type": "input"},
                                {"name": "VOUT", "type": "output"}
                            ],
                            "nets": [
                                {
                                    "name": "VIN_NET",
                                    "connection": ["VIN", "C1.1"]
                                }
                            ],
                            "buses": []
                        }
                    ],
                    "pins": ["VDD", "GND"],
                    "nets": [
                        {
                            "name": "VDD_NET",
                            "connection": ["R1.1", "power.VIN"]
                        }
                    ],
                    "buses": []
                }
            ],
            "components": [
                {
                    "name": "RES",
                    "class": "",
                    "partno": "",
                    "package": "",
                    "pins": [
                        {"id": 1},
                        {"id": 2}
                    ]
                },
                {
                    "name": "CAP",
                    "class": "",
                    "partno": "",
                    "package": "",
                    "pins": [
                        {"id": 1},
                        {"id": 2}
                    ]
                }
            ]
        }
        "#;

        CircuitParser::parse_from_str(test_json).unwrap()
    }

    #[test]
    fn test_parse_reference_file() {
        // Note: The reference file contains JSON comments which are not valid JSON
        // This test demonstrates that the parser correctly identifies the JSON syntax error
        let result = CircuitParser::parse_from_file("refs/hbl.json");

        // We expect this to fail due to JSON comments in the file
        assert!(result.is_err(), "Reference file should fail to parse due to JSON comments");

        // Verify it's a JSON error
        match result.unwrap_err() {
            CircuitParseError::JsonError(_) => {
                // This is expected - the file contains JSON comments
            }
            other => panic!("Expected JsonError, got {:?}", other),
        }
    }

    #[test]
    fn test_parse_from_string() {
        let circuit = create_test_circuit();
        assert_eq!(circuit.modules.len(), 1);
        assert_eq!(circuit.components.len(), 2);
    }

    #[test]
    fn test_main_module_access() {
        let circuit = create_test_circuit();
        let main_module = circuit.main_module();
        assert!(main_module.is_some());

        let main = main_module.unwrap();
        assert_eq!(main.class, "main");
        assert_eq!(main.instance, "main");
    }

    #[test]
    fn test_find_module() {
        let circuit = create_test_circuit();

        // Find main module
        let main_module = circuit.find_module("main");
        assert!(main_module.is_some());

        // Find nested module
        let power_module = circuit.find_module("power");
        assert!(power_module.is_some());
        assert_eq!(power_module.unwrap().class, "POWER");

        // Try to find non-existent module
        let missing_module = circuit.find_module("nonexistent");
        assert!(missing_module.is_none());
    }

    #[test]
    fn test_find_component_definition() {
        let circuit = create_test_circuit();

        let res_def = circuit.find_component_definition("RES");
        assert!(res_def.is_some());
        assert_eq!(res_def.unwrap().pins.len(), 2);

        let cap_def = circuit.find_component_definition("CAP");
        assert!(cap_def.is_some());

        let missing_def = circuit.find_component_definition("MISSING");
        assert!(missing_def.is_none());
    }

    #[test]
    fn test_all_modules() {
        let circuit = create_test_circuit();
        let all_modules = circuit.all_modules();
        assert_eq!(all_modules.len(), 2); // main + power
    }

    #[test]
    fn test_all_components() {
        let circuit = create_test_circuit();
        let all_components = circuit.all_components();
        assert_eq!(all_components.len(), 2); // R1 + C1
    }

    #[test]
    fn test_module_methods() {
        let circuit = create_test_circuit();
        let main_module = circuit.main_module().unwrap();

        // Test find_component
        let r1 = main_module.find_component("R1");
        assert!(r1.is_some());
        assert_eq!(r1.unwrap().class, "RES");

        // Test components()
        let components = main_module.components();
        assert_eq!(components.len(), 1); // Only R1 in main module directly

        // Test nested_modules()
        let nested = main_module.nested_modules();
        assert_eq!(nested.len(), 1); // power module
        assert_eq!(nested[0].instance, "power");
    }

    #[test]
    fn test_component_parameters() {
        let circuit = create_test_circuit();
        let main_module = circuit.main_module().unwrap();
        let r1 = main_module.find_component("R1").unwrap();

        // Test parameter access
        assert_eq!(r1.get_param_str("rs"), Some("10kΩ"));
        assert_eq!(r1.get_param_str("pkg"), Some("R0603"));
        assert_eq!(r1.get_param_str("missing"), None);

        // Test has_param
        assert!(r1.has_param("rs"));
        assert!(!r1.has_param("missing"));

        // Test is_not_connected
        assert!(!r1.is_not_connected());
    }

    #[test]
    fn test_validation_errors() {
        // Test empty modules
        let empty_json = r#"{"modules": [], "components": []}"#;
        let result = CircuitParser::parse_from_str(empty_json);
        assert!(result.is_err());

        // Test missing required fields
        let invalid_json = r#"
        {
            "modules": [
                {
                    "type": "module",
                    "class": "",
                    "instance": "test"
                }
            ]
        }
        "#;
        let result = CircuitParser::parse_from_str(invalid_json);
        assert!(result.is_err());
    }

    #[test]
    fn test_malformed_json() {
        let malformed_json = r#"{"modules": [{"invalid": json}]}"#;
        let result = CircuitParser::parse_from_str(malformed_json);
        assert!(result.is_err());

        match result.unwrap_err() {
            CircuitParseError::JsonError(_) => {}, // Expected
            other => panic!("Expected JsonError, got {:?}", other),
        }
    }

    #[test]
    fn test_comprehensive_validation() {
        let circuit = create_test_circuit();
        let result = CircuitParser::validate_comprehensive(&circuit);
        assert!(result.is_ok(), "Comprehensive validation should pass: {:?}", result.err());
    }

    #[test]
    fn test_nc_component() {
        let nc_json = r#"
        {
            "modules": [
                {
                    "type": "module",
                    "class": "test",
                    "instance": "test",
                    "objects": [
                        {
                            "type": "component",
                            "class": "RES",
                            "instance": "R_NC",
                            "params": {
                                "rs": "10kΩ",
                                "connection": "NC"
                            }
                        }
                    ]
                }
            ]
        }
        "#;

        let circuit = CircuitParser::parse_from_str(nc_json).unwrap();
        let module = &circuit.modules[0];
        let component = module.find_component("R_NC").unwrap();

        assert!(component.is_not_connected());
    }
}